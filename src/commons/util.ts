export function timeframe2timeUnit(timeframe: string): number {
    switch (timeframe) {
        case '1m':
            return 60 * 1000
        case '15m':
            return 15 * 60 * 1000
        case '3m':
            return 3 * 60 * 1000
        case '5m':
            return 5 * 60 * 1000
        case '30m':
            return 30 * 60 * 1000
        case '1h':
            return 60 * 60 * 1000
        case '4h':
            return 4 * 60 * 60 * 1000
        case '1d':
            return 24 * 60 * 60 * 1000
        default:
            throw new Error('invalid timeframe')
    }
}


export function findClosestDivisible(
    number: number,
    divisor: number,
    snapDirection: 'lower' | 'higher' | 'closest' = 'closest'
): number {
    const lower = Math.floor(number / divisor) * divisor;
    const higher = Math.ceil(number / divisor) * divisor;

    switch (snapDirection) {
        case 'lower':
            return lower;
        case 'higher':
            return higher;
        case 'closest':
        default:
            return Math.abs(number - lower) < Math.abs(higher - number) ? lower : higher;
    }
}

export function disableDebug() {
    console.debug = function () { };
}
