

import { type TDataPoint } from "../types/plot.d";
import { ListStore } from "./listStore";
import { isNil } from 'es-toolkit'

export class HistogramStore extends ListStore<TDataPoint> {
    readonly type = 'histogram'
    getValueRangeByTime(from: number, to: number) {
        const selected = this.selectByTime(from, to);
        if (selected.length === 0) return

        let max = -Infinity;
        let min = Infinity;
        for (const dp of selected) {
            if (isNil(dp.value)) continue;
            if (dp.value > max) max = dp.value;
            if (dp.value < min) min = dp.value;
        }
        return { max, min };
    }
}
