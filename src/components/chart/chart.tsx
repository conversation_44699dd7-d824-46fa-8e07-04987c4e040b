import { ChartStore } from "../../store/chartStore"
import { ChartContainer } from "../../styles/chartStyles"
import { ChartContextProvider } from "../../contexts/chartContext"

import { YAxis } from "./yAxis"
import { YAXIS_WIDTH } from "../../commons/constants"
import { Pane } from "./pane"

export const Chart = (props: { style?: React.CSSProperties, store: ChartStore, width: number, height: number }) => {
    console.debug('rendering chart')

    return (
        <ChartContainer style={props.style}>
            <ChartContextProvider chartStore={props.store} width={props.width} height={props.height}>
                <Pane width={props.width - YAXIS_WIDTH} height={props.height} />
                <YAxis width={YAXIS_WIDTH} height={props.height} />
            </ChartContextProvider>
        </ChartContainer>
    )
}