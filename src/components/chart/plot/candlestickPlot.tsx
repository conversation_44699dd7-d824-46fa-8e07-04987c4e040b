import { Shape } from "react-konva";
import { type CandlestickStore } from "../../../store/candlestickStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";

export const CandlestickPlot = (props: { store: CandlestickStore }) => {
    console.debug('rendering candlestick plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x, deltaT2x } = usePaneScale()



    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)
        let barWidth = deltaT2x(timeUnit) - (store.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);
        const minHeightForRoundCorners = cornerRadius * 3;

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? store.config?.riseColor : store.config?.fallColor)
            const fill = color

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw wicks with sketchy effect
                const wickWidth = Math.min(barWidth, 2);
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', wickWidth);

                // Upper wick with slight randomness
                context.beginPath();
                let currentY = highY;
                while (currentY < Math.min(closeY, openY)) {
                    const nextY = Math.min(currentY + 2, Math.min(closeY, openY));
                    const offsetX = (Math.random() - 0.5) * 0.5;
                    context.moveTo(x + offsetX, currentY);
                    context.lineTo(x + offsetX, nextY);
                    currentY = nextY;
                }
                context.stroke();

                // Lower wick with slight randomness
                context.beginPath();
                currentY = Math.max(closeY, openY);
                while (currentY < lowY) {
                    const nextY = Math.min(currentY + 2, lowY);
                    const offsetX = (Math.random() - 0.5) * 0.5;
                    context.moveTo(x + offsetX, currentY);
                    context.lineTo(x + offsetX, nextY);
                    currentY = nextY;
                }
                context.stroke();

                // Draw body
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

                // Fill base color
                context.beginPath();
                if (bodyHeight < minHeightForRoundCorners) {
                    context.rect(bodyX, bodyY, barWidth, bodyHeight);
                } else {
                    context.moveTo(bodyX + cornerRadius, bodyY);
                    context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                    context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                    context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                    context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                    context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                    context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                    context.lineTo(bodyX, bodyY + cornerRadius);
                    context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                }

                context.setAttr('fillStyle', fill);
                context.setAttr('globalAlpha', 0.9);
                context.fill();
                context.setAttr('globalAlpha', 1);

                // Add sketch effect for wider bars
                if (barWidth > 4) {
                    addSketchEffect(context, bodyX, bodyY, barWidth, bodyHeight, fill);
                }
            }
        }
    }


    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )


    function addSketchEffect(context: Konva.Context, x: number, y: number, width: number, height: number, color: string) {
        // Skip effects for very small bars
        if (width < 4 || height < 4) {
            context.fillStyle = color;
            context.fillRect(x, y, width, height);
            return;
        }

        context.save();

        // Calculate effectColor inside the function
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        const isDark = (r * 0.299 + g * 0.587 + b * 0.114) < 128;
        const effectColor = isDark
            ? `#${Math.min(255, r + 100).toString(16).padStart(2, '0')}${Math.min(255, g + 100).toString(16).padStart(2, '0')}${Math.min(255, b + 100).toString(16).padStart(2, '0')}`
            : `#${Math.max(0, r - 60).toString(16).padStart(2, '0')}${Math.max(0, g - 60).toString(16).padStart(2, '0')}${Math.max(0, b - 60).toString(16).padStart(2, '0')}`;

        // Base fill
        context.fillStyle = color;
        context.fillRect(x, y, width, height);

        // Clip to candlestick area
        context.beginPath();
        context.rect(x, y, width, height);
        context.clip();

        // Adjust detail level based on area
        const area = width * height;
        const detailLevel = area < 100 ? 'low' : area < 300 ? 'medium' : 'high';

        // Diagonal lines (batched)
        if (detailLevel !== 'low') {
            const spacing = detailLevel === 'medium' ? 8 : 6;
            context.globalAlpha = 0.4;
            context.strokeStyle = effectColor;
            context.lineWidth = 1;

            context.beginPath();
            for (let i = -height; i < width + height; i += spacing) {
                context.moveTo(x + i, y);
                context.lineTo(x + i + height, y + height);
            }
            context.stroke();
        }

        // Edges (batched and simplified)
        context.globalAlpha = 0.5;
        context.strokeStyle = effectColor;
        context.lineWidth = 1;

        context.beginPath();
        context.moveTo(x, y);
        context.lineTo(x + width, y);
        context.lineTo(x + width, y + height);
        context.lineTo(x, y + height);
        context.closePath();
        context.stroke();

        // Texture dots (only for high detail)
        if (detailLevel === 'high') {
            context.globalAlpha = 0.35;
            const dotSpacing = 10;
            const maxDots = 50;
            const stepX = Math.max(dotSpacing, width / Math.sqrt(maxDots));
            const stepY = Math.max(dotSpacing, height / Math.sqrt(maxDots));

            for (let dx = 0; dx < width; dx += stepX) {
                for (let dy = 0; dy < height; dy += stepY) {
                    const offsetX = Math.sin((dx + dy) * 0.5);
                    const offsetY = Math.cos((dx + dy) * 0.5);

                    context.beginPath();
                    context.arc(
                        x + dx + offsetX,
                        y + dy + offsetY,
                        1,
                        0,
                        Math.PI * 2
                    );
                    context.fillStyle = effectColor;
                    context.fill();
                }
            }
        }

        context.restore();
    }
}
