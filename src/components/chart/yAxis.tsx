import { Layer, Stage } from 'react-konva'
import { useChartContext } from '../../contexts/chartContext';
import Konva from 'konva';
import { useAppContext } from '../../contexts/appContext';
import { usePaneScale } from '../../hooks/useScale';

export const YAxis = (props: { width: number, height: number }) => {
    console.debug('rendering y axis')
    const { valueRange, autoScale, } = useChartContext()
    const { resetValueRange } = usePaneScale()
    const { activeChart, crosshairsCoord } = useAppContext()

    return (
        <Stage
            width={props.width}
            height={props.height}
            onWheel={onWheel}
            onDblClick={onDblClick}
            onMouseEnter={onMouseEnter}
        >
            <Layer>
            </Layer>
        </Stage>
    )

    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsCoord.value = null
        activeChart.value = ''
    }

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.evt.preventDefault()
        const wheelDelta = e.evt.deltaY
        const zoomFactor = Math.pow(0.999, -wheelDelta);

        const prev = valueRange.value
        if (!prev) return
        const span = prev.max - prev.min
        const newSpan = span * zoomFactor
        const centerTime = prev.min + (span / 2)
        const newMin = centerTime - (newSpan / 2)
        const newMax = centerTime + (newSpan / 2)
        valueRange.value = {
            min: newMin,
            max: newMax
        }
        autoScale.value = false
    }

    function onDblClick(_e: Konva.KonvaEventObject<MouseEvent>) {
        autoScale.value = true
        resetValueRange()
    }
}
