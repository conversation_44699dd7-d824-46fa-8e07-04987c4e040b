import { Group, Line } from "react-konva";
import { useAppContext } from "../contexts/appContext";
import { useChartContext } from "../contexts/chartContext";
import { useEffect } from "react";


const CROSSHAIR_COLOR = '#16368e';

export const Crosshairs = () => {
    const { activeChart, crosshairsCoord } = useAppContext()
    const { paneHeight, paneWidth, chartStore } = useChartContext()
    crosshairsCoord.use()
    activeChart.use()


    return (
        crosshairsCoord.value && (
            <Group listening={false}>
                <Line
                    points={[0, crosshairsCoord.value.y, paneWidth, crosshairsCoord.value.y]}
                    visible={activeChart.value === chartStore.name && activeChart.value !== ''}
                    strokeWidth={1.5}
                    stroke={CROSSHAIR_COLOR}
                    dash={[4, 4]}
                />
                <Line
                    points={[crosshairsCoord.value.x, 0, crosshairsCoord.value.x, paneHeight]}
                    visible={activeChart.value !== ''}
                    strokeWidth={1.5}
                    stroke={CROSSHAIR_COLOR}
                    dash={[4, 4]}
                />
            </Group>
        )
    );
}
