import { styled } from "@stitches/react";

export const ChartGroupContainer = styled('div', {
    flex: 1,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    overflow: 'hidden',
})

export const ChartContainer = styled('div', {
    display: 'flex',
    flexDirection: 'row',
    position: 'relative',
    width: '100%',
    height: '100%',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
});