import { styled } from "@stitches/react";


export const LiquidGlassDiv = styled('div', {
    position: 'relative',
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(3px)',
    borderRadius: '10px',
    overflow: 'hidden',

    // Create curved light refraction effect around edges using radial gradients
    '&::before': {
        content: '""',
        position: 'absolute',
        top: '-2px',
        left: '-2px',
        right: '-2px',
        bottom: '-2px',
        borderRadius: '12px',
        background: `
            radial-gradient(circle at 0% 0%, rgba(255, 255, 255, 0.6) 0%, transparent 50%),
            radial-gradient(circle at 100% 0%, rgba(255, 255, 255, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 100% 100%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 0% 100%, rgba(255, 255, 255, 0.5) 0%, transparent 50%),
            conic-gradient(from 0deg at 50% 50%,
                rgba(255, 255, 255, 0.3) 0deg,
                rgba(255, 255, 255, 0.1) 90deg,
                rgba(255, 255, 255, 0.2) 180deg,
                rgba(255, 255, 255, 0.1) 270deg,
                rgba(255, 255, 255, 0.3) 360deg
            )
        `,
        mask: `
            radial-gradient(circle at center, transparent 10px, black 12px),
            linear-gradient(black, black)
        `,
        maskComposite: 'subtract',
        WebkitMask: `
            radial-gradient(circle at center, transparent 10px, black 12px),
            linear-gradient(black, black)
        `,
        WebkitMaskComposite: 'subtract',
        pointerEvents: 'none',
        zIndex: 1,
    },

    // Inner curved light highlights that follow the rounded corners
    '&::after': {
        content: '""',
        position: 'absolute',
        top: '1px',
        left: '1px',
        right: '1px',
        bottom: '1px',
        borderRadius: '9px',
        background: `
            radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.4) 0%, transparent 40%),
            radial-gradient(ellipse at top right, rgba(255, 255, 255, 0.2) 0%, transparent 40%),
            radial-gradient(ellipse at bottom right, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
            radial-gradient(ellipse at bottom left, rgba(255, 255, 255, 0.25) 0%, transparent 40%)
        `,
        pointerEvents: 'none',
        zIndex: 2,
    },

    // Ensure content appears above the glass effects
    '& > *': {
        position: 'relative',
        zIndex: 3,
    },

    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',

    '&:hover': {
        background: 'rgba(255, 255, 255, 0.08)',

        '&::before': {
            background: `
                radial-gradient(circle at 0% 0%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
                radial-gradient(circle at 100% 0%, rgba(255, 255, 255, 0.6) 0%, transparent 50%),
                radial-gradient(circle at 100% 100%, rgba(255, 255, 255, 0.5) 0%, transparent 50%),
                radial-gradient(circle at 0% 100%, rgba(255, 255, 255, 0.7) 0%, transparent 50%),
                conic-gradient(from 0deg at 50% 50%,
                    rgba(255, 255, 255, 0.4) 0deg,
                    rgba(255, 255, 255, 0.15) 90deg,
                    rgba(255, 255, 255, 0.3) 180deg,
                    rgba(255, 255, 255, 0.15) 270deg,
                    rgba(255, 255, 255, 0.4) 360deg
                )
            `,
        }
    }
})