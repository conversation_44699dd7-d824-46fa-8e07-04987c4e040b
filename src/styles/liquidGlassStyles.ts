import { styled } from "@stitches/react";


export const LiquidGlassDiv = styled('div', {
    position: 'relative',
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(3px)',
    borderRadius: '10px',

    // Remove traditional border and create glass effect with shadows and pseudo-elements
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: '10px',
        padding: '1px',
        background: `
            linear-gradient(135deg,
                rgba(255, 255, 255, 0.4) 0%,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 75%,
                rgba(255, 255, 255, 0.3) 100%
            )
        `,
        mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
        maskComposite: 'xor',
        WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
        WebkitMaskComposite: 'xor',
        pointerEvents: 'none',
    },

    // Inner highlight for glass refraction effect
    '&::after': {
        content: '""',
        position: 'absolute',
        top: '1px',
        left: '1px',
        right: '1px',
        bottom: '1px',
        borderRadius: '9px',
        background: `
            linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                transparent 30%,
                transparent 70%,
                rgba(255, 255, 255, 0.08) 100%
            )
        `,
        pointerEvents: 'none',
    },

    // Multiple layered box shadows for depth and light refraction
    boxShadow: `
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1),
        inset 1px 0 0 rgba(255, 255, 255, 0.1),
        inset -1px 0 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05)
    `,

    // Subtle animation for living glass effect
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',

    '&:hover': {
        background: 'rgba(255, 255, 255, 0.08)',
        boxShadow: `
            inset 0 1px 0 rgba(255, 255, 255, 0.25),
            inset 0 -1px 0 rgba(255, 255, 255, 0.15),
            inset 1px 0 0 rgba(255, 255, 255, 0.15),
            inset -1px 0 0 rgba(255, 255, 255, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.08),
            0 4px 8px rgba(0, 0, 0, 0.15),
            0 12px 24px rgba(0, 0, 0, 0.08)
        `,
    }
})