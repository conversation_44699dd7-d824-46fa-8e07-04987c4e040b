import { styled } from "@stitches/react";


export const LiquidGlassDiv = styled('div', {
    position: 'relative',
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(3px)',
    borderRadius: '10px',
    overflow: 'visible',

    // Create light refraction slashes that curve based on edge curvature
    '&::before': {
        content: '""',
        position: 'absolute',
        top: '-1px',
        left: '-1px',
        right: '-1px',
        bottom: '-1px',
        borderRadius: '11px',
        background: `
            // Top edge - straight light slashes diagonally
            linear-gradient(45deg,
                transparent 0%,
                rgba(255, 255, 255, 0.6) 1px,
                transparent 2px,
                transparent 4px,
                rgba(255, 255, 255, 0.3) 5px,
                transparent 6px
            ),
            // Right edge - light slashes curve inward
            linear-gradient(135deg,
                transparent 0%,
                rgba(255, 255, 255, 0.5) 1px,
                transparent 2px,
                transparent 3px,
                rgba(255, 255, 255, 0.4) 4px,
                transparent 5px
            ),
            // Bottom edge - light slashes curve outward
            linear-gradient(225deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 1px,
                transparent 2px,
                transparent 4px,
                rgba(255, 255, 255, 0.2) 5px,
                transparent 6px
            ),
            // Left edge - light slashes curve back
            linear-gradient(315deg,
                transparent 0%,
                rgba(255, 255, 255, 0.5) 1px,
                transparent 2px,
                transparent 3px,
                rgba(255, 255, 255, 0.3) 4px,
                transparent 5px
            )
        `,
        mask: `
            linear-gradient(#000 0 0) content-box,
            linear-gradient(#000 0 0)
        `,
        maskComposite: 'xor',
        WebkitMask: `
            linear-gradient(#000 0 0) content-box,
            linear-gradient(#000 0 0)
        `,
        WebkitMaskComposite: 'xor',
        padding: '1px',
        pointerEvents: 'none',
        zIndex: 1,
    },

    // Corner light bending effects where curvature is strongest
    '&::after': {
        content: '""',
        position: 'absolute',
        top: '-2px',
        left: '-2px',
        right: '-2px',
        bottom: '-2px',
        borderRadius: '12px',
        background: `
            // Top-left corner - light curves 45 degrees
            conic-gradient(from 225deg at 10px 10px,
                rgba(255, 255, 255, 0.7) 0deg,
                transparent 45deg,
                transparent 315deg,
                rgba(255, 255, 255, 0.7) 360deg
            ),
            // Top-right corner - light curves -45 degrees
            conic-gradient(from 135deg at calc(100% - 10px) 10px,
                rgba(255, 255, 255, 0.5) 0deg,
                transparent 45deg,
                transparent 315deg,
                rgba(255, 255, 255, 0.5) 360deg
            ),
            // Bottom-right corner - light curves 45 degrees
            conic-gradient(from 45deg at calc(100% - 10px) calc(100% - 10px),
                rgba(255, 255, 255, 0.4) 0deg,
                transparent 45deg,
                transparent 315deg,
                rgba(255, 255, 255, 0.4) 360deg
            ),
            // Bottom-left corner - light curves -45 degrees
            conic-gradient(from 315deg at 10px calc(100% - 10px),
                rgba(255, 255, 255, 0.6) 0deg,
                transparent 45deg,
                transparent 315deg,
                rgba(255, 255, 255, 0.6) 360deg
            )
        `,
        mask: `
            radial-gradient(circle at 10px 10px, black 8px, transparent 12px),
            radial-gradient(circle at calc(100% - 10px) 10px, black 8px, transparent 12px),
            radial-gradient(circle at calc(100% - 10px) calc(100% - 10px), black 8px, transparent 12px),
            radial-gradient(circle at 10px calc(100% - 10px), black 8px, transparent 12px)
        `,
        maskComposite: 'add',
        WebkitMask: `
            radial-gradient(circle at 10px 10px, black 8px, transparent 12px),
            radial-gradient(circle at calc(100% - 10px) 10px, black 8px, transparent 12px),
            radial-gradient(circle at calc(100% - 10px) calc(100% - 10px), black 8px, transparent 12px),
            radial-gradient(circle at 10px calc(100% - 10px), black 8px, transparent 12px)
        `,
        WebkitMaskComposite: 'add',
        pointerEvents: 'none',
        zIndex: 2,
    },

    // Ensure content appears above the glass effects
    '& > *': {
        position: 'relative',
        zIndex: 3,
    },

    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
})