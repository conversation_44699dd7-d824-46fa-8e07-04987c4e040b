import { ChartGroup } from './components/chartGroup';
import { AppContextProvider } from './contexts/appContext';
import { DataContextProvider } from './contexts/dataContext';
import { LayoutContextProvider } from './contexts/layoutContext';
import { AppContainer, Footer, globalStyles, Header } from './styles/appStyles';

import { useEffect, useRef } from 'react';
import { useSignal } from './hooks/useSignal';
import { XAxis } from './components/xAxis';
import { Selector } from './components/selector';
import { fetchSymbolList, fetchTimeframeList } from './commons/api';
import { disableDebug } from './commons/util';
import { LiquidGlassDiv } from './styles/liquidGlassStyles';
disableDebug()


export const App = () => {
  console.debug('rendering app')
  globalStyles();
  const appContainer = useRef<HTMLDivElement>(null);
  const symbolOptions = useSignal<{ label: string, value: string }[]>([])
  const timeframeOptions = useSignal<{ label: string, value: string }[]>([])
  symbolOptions.use()
  timeframeOptions.use()

  const symbol = useSignal('btc')
  const timeframe = useSignal('3m')
  symbol.use()
  timeframe.use()

  useEffect(() => {
    fetchSymbolList().then(data => {
      symbolOptions.value = data.map((item: string) => ({ label: item.toUpperCase(), value: item }))
    })
    fetchTimeframeList().then(data => {
      timeframeOptions.value = data.map((item: string) => ({ label: item, value: item }))
    })
  }, [])

  return (
    <AppContextProvider
      symbol={symbol.value}
      timeframe={timeframe.value}
    >
      <DataContextProvider>
        <AppContainer ref={appContainer}>
          <LayoutContextProvider container={appContainer}>
            <ChartGroup />
            <Footer>
              <XAxis />
            </Footer>

            <Header>
              <Selector
                value={symbol.value}
                options={symbolOptions.value}
                onSelectChange={value => symbol.value = value}
              />
              <Selector
                value={timeframe.value}
                options={timeframeOptions.value}
                onSelectChange={value => timeframe.value = value}
              />
              <LiquidGlassDiv style={{ height: 100 }}>
                Apple Liquid Glass
              </LiquidGlassDiv>
            </Header>
          </LayoutContextProvider>
        </AppContainer>
      </DataContextProvider>
    </AppContextProvider>
  )
}
