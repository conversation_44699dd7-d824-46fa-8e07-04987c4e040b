
import { DEFAULT_CHART_PADDING_RATIO } from "../commons/constants";
import { useAppContext } from "../contexts/appContext";
import { useChartContext } from "../contexts/chartContext";

import { TRange } from "../types/common";
import { type TSignal } from "./useSignal";

export const usePaneScale = () => {
    console.debug('use pane scale')
    const { timeRange } = useAppContext()
    const { valueRange, paneWidth, paneHeight, chartStore } = useChartContext()

    const hooks = useScale({
        width: paneWidth,
        height: paneHeight,
        valueRange,
        timeRange
    })

    const resetValueRange = () => {
        const chartValueRange = chartStore.getValueRangeByTime(timeRange.value.min, timeRange.value.max)
        if (chartValueRange) {
            const span = chartValueRange.max - chartValueRange.min
            const padding = span * DEFAULT_CHART_PADDING_RATIO
            valueRange.value = {
                min: chartValueRange.min - padding,
                max: chartValueRange.max + padding
            }
        }
    }

    return {
        ...hooks,
        resetValueRange
    }
}


export const useScale = (props: { width: number, height: number, valueRange: TSignal<TRange>, timeRange: TSignal<TRange> }) => {
    console.debug('use scale')
    const { width, height, valueRange, timeRange } = props
    const valueSpan = valueRange.value.max - valueRange.value.min
    const timeSpan = timeRange.value.max - timeRange.value.min

    const v2y = (d: number) => {
        if (!valueRange || !height) return 0;
        if (valueSpan <= 0) return 0;
        // Invert Y-axis (0 at top, height at bottom)
        return height - ((d - valueRange.value.min) / valueSpan * height);
    }

    const y2v = (c: number) => {
        if (!valueRange || !height) return 0;
        if (valueSpan <= 0) return 0;
        // Invert Y-axis (0 at top, height at bottom)
        return valueRange.value.min + (valueSpan * (height - c) / height);
    }

    const t2x = (d: number) => {
        if (!timeRange || !width) return 0;
        if (timeSpan <= 0) return 0;
        return ((d - timeRange.value.min) / timeSpan) * width;
    }

    const x2t = (c: number) => {
        if (!timeRange || !width) return 0;
        if (timeSpan <= 0) return 0;
        return timeRange.value.min + (timeSpan * c / width);
    }

    const deltaX2t = (deltaX: number) => {
        if (!timeRange || !width) return 0;
        if (timeSpan <= 0) return 0;
        return timeSpan * deltaX / width;
    }

    const deltaY2v = (deltaY: number) => {
        if (!valueRange || !height) return 0;
        if (valueSpan <= 0) return 0;
        return valueSpan * deltaY / height;
    }

    const deltaT2x = (deltaT: number) => {
        if (!timeRange || !width) return 0;
        if (timeSpan <= 0) return 0;
        return (deltaT / timeSpan) * width;
    }

    const deltaV2y = (deltaV: number) => {
        if (!valueRange || !height) return 0;
        if (valueSpan <= 0) return 0;
        return (deltaV / valueSpan) * height;
    }

    return {
        v2y,
        y2v,
        t2x,
        x2t,
        deltaX2t,
        deltaY2v,
        deltaT2x,
        deltaV2y,
    }
}